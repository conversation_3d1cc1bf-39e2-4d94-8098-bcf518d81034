/*
 * ========================================
 * 文件名: Program.cs
 * 功能描述: 应用程序主入口点
 * ========================================
 *
 * 主要功能:
 * 1. 应用程序启动和初始化
 * 2. Aspose.Words许可证加载和验证
 * 3. 配置目录创建和管理
 * 4. 异常处理和错误提示
 *
 * 核心职责:
 * - 程序启动时的环境准备工作
 * - 许可证文件的加载（支持嵌入资源和外部文件两种方式）
 * - 确保必要的配置目录存在
 * - 启动主窗体界面
 *
 * 依赖关系:
 * - MainForm: 主窗体类
 * - Aspose.Words: 文档处理核心库
 * - SettingsManager: 配置管理（间接依赖）
 *
 * 注意事项:
 * - 许可证加载失败时程序仍可运行，但会有水印限制
 * - 支持从嵌入资源或外部文件加载许可证
 * - 自动处理配置目录的创建和迁移
 */

using System;
using System.Windows.Forms;
using System.IO;
using System.Reflection;

namespace AsposeWordFormatter
{
    static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                // 1. 确保配置目录存在
                EnsureConfigDirectoryExists();

                // 2. 初始化Logger单例（确保日志系统最早启动）
                var logger = Logger.Instance;
                logger.Log("=== 应用程序启动 ===");
                logger.Log($"程序启动时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                logger.Log($"程序目录: {AppDomain.CurrentDomain.BaseDirectory}");

                // 3. 加载Aspose.Words许可证
                LoadAsposeWordsLicense(logger);

                // 4. 设置WinForms应用程序属性
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                // 5. 启动主窗体
                logger.Log("正在启动主窗体...");
                Application.Run(new MainForm());

                logger.Log("应用程序正常退出");
            }
            catch (Exception ex)
            {
                // 应急错误处理
                string errorMessage = $"应用程序启动失败: {ex.Message}";
                Console.WriteLine(errorMessage);

                try
                {
                    Logger.Instance.LogError("应用程序启动失败", ex);
                }
                catch
                {
                    // 如果连日志都无法记录，只能输出到控制台
                }

                MessageBox.Show($"{errorMessage}\n\n详细信息:\n{ex}",
                    "启动错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 加载Aspose.Words许可证
        /// </summary>
        private static void LoadAsposeWordsLicense(Logger logger)
        {
            try
            {
                logger.Log("开始加载Aspose.Words许可证...");

                // 使用嵌入资源加载许可证
                var license = new Aspose.Words.License();

                // 从程序集中获取嵌入的许可证资源流
                var assembly = Assembly.GetExecutingAssembly();
                Stream? licenseStream = assembly.GetManifestResourceStream("AsposeWordFormatter.Aspose.Total.NET.lic");

                using (licenseStream)
                {
                    if (licenseStream != null)
                    {
                        license.SetLicense(licenseStream);
                        logger.Log("Aspose.Words 许可证已从嵌入资源成功加载");
                    }
                    else
                    {
                        // 如果嵌入资源未找到，尝试从文件加载（作为备用方法）
                        string licPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Aspose.Total.NET.lic");
                        if (File.Exists(licPath))
                        {
                            license.SetLicense(licPath);
                            logger.Log("Aspose.Words 许可证已从外部文件成功加载");
                        }
                        else
                        {
                            logger.LogWarning("未找到Aspose.Words许可证，将使用评估模式");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogError("加载Aspose.Words许可证时出错", ex);
                MessageBox.Show($"加载Aspose.Words许可证时出错: {ex.Message}\n\n应用将以评估模式运行。",
                    "许可证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 确保配置目录和配置文件存在
        /// </summary>
        private static void EnsureConfigDirectoryExists()
        {
            try
            {
                // 配置目录路径
                string configDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config");

                // 如果目录不存在，创建它
                if (!Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir);
                    Console.WriteLine($"已创建配置目录: {configDir}");

                    // 检查是否存在旧的配置文件，如果有则迁移
                    string oldSettingsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "settings.json");
                    if (File.Exists(oldSettingsPath))
                    {
                        // 旧配置将在SettingsManager.LoadSettings中自动迁移
                        Console.WriteLine("检测到旧版配置文件，将在首次运行时自动迁移到新的配置结构");
                    }
                    else
                    {
                        // 如果没有旧配置，则首次运行会创建默认配置
                        Console.WriteLine("未检测到现有配置，将创建默认配置文件");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"创建配置目录时出错: {ex.Message}");
                MessageBox.Show($"创建配置目录时出错: {ex.Message}\n\n应用将使用内存中的默认设置。",
                    "配置错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }
    }
}