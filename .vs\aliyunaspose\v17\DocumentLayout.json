{"Version": 1, "WorkspaceRootPath": "D:\\Cursor\\aliyunaspose\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{3C8CD6D7-5523-42E6-828D-F222B7EDAB6B}|aliyunaspose\\aliyunaspose.csproj|d:\\cursor\\aliyunaspose\\aliyunaspose\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3C8CD6D7-5523-42E6-828D-F222B7EDAB6B}|aliyunaspose\\aliyunaspose.csproj|solutionrelative:aliyunaspose\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "MainWindow.xaml.cs", "DocumentMoniker": "D:\\Cursor\\aliyunaspose\\aliyunaspose\\MainWindow.xaml.cs", "RelativeDocumentMoniker": "aliyunaspose\\MainWindow.xaml.cs", "ToolTip": "D:\\Cursor\\aliyunaspose\\aliyunaspose\\MainWindow.xaml.cs", "RelativeToolTip": "aliyunaspose\\MainWindow.xaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-30T04:58:11.972Z", "EditorCaption": ""}]}]}]}