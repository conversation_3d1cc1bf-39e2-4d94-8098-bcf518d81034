/*
 * ========================================
 * 文件名: Logger.cs
 * 功能描述: 分级日志记录系统
 * ========================================
 *
 * 主要功能:
 * 1. 多级别日志记录（Debug、Info、Warning、Error、Fatal）
 * 2. 分级日志文件存储和管理
 * 3. 日志缓冲和异步写入机制
 * 4. 日志格式化和配置选项
 * 5. 内存使用监控和性能计时
 * 6. 日志清理和维护功能
 *
 * 核心特性:
 * - 支持按日期自动创建日志文件
 * - 实现了内存缓冲机制提高性能
 * - 支持分级日志文件（按级别分别存储）
 * - 包含完整的日志开关控制
 * - 提供计时器功能用于性能分析
 * - 支持内存使用情况监控
 *
 * 日志级别:
 * - Debug: 调试信息，开发时使用
 * - Info: 一般信息，默认记录级别
 * - Warning: 警告信息，表示潜在问题
 * - Error: 错误信息，表示操作失败
 * - Fatal: 致命错误，可能导致程序崩溃
 *
 * 文件结构:
 * - logs/log_yyyy-MM-dd.txt: 总日志文件
 * - logs/debug_yyyy-MM-dd.txt: 调试级别日志
 * - logs/info_yyyy-MM-dd.txt: 信息级别日志
 * - logs/warning_yyyy-MM-dd.txt: 警告级别日志
 * - logs/error_yyyy-MM-dd.txt: 错误级别日志
 * - logs/fatal_yyyy-MM-dd.txt: 致命错误级别日志
 *
 * 配置选项:
 * - EnableLogging: 总日志开关
 * - UseSeparateLogFiles: 是否使用分级日志文件
 * - KeepAllLogFile: 是否保留总日志文件
 * - MinimumLogLevel: 最低记录级别
 * - FormatOptions: 日志格式选项
 *
 * 注意事项:
 * - 所有日志操作都是线程安全的
 * - 支持大文件自动分割（超过10MB）
 * - 实现了自动日期切换功能
 * - 包含完整的异常处理机制
 */

using System;
using System.IO;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Diagnostics;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;

namespace AsposeWordFormatter
{
    /// <summary>
    /// 日志级别，用于区分不同类型的日志信息
    /// </summary>
    public enum LogLevel
    {
        Debug,      // 调试信息，仅在开发时有用
        Info,       // 一般信息，默认记录级别
        Warning,    // 警告信息，表示潜在问题
        Error,      // 错误信息，表示操作失败
        Fatal       // 致命错误，可能导致程序崩溃
    }

    /// <summary>
    /// 日志格式选项，控制日志输出的格式
    /// </summary>
    [Flags]
    public enum LogFormatOptions
    {
        None = 0,
        IncludeTimestamp = 1,       // 包含时间戳
        IncludeLevel = 2,           // 包含日志级别
        IncludeThreadId = 4,        // 包含线程ID
        IncludeMemoryUsage = 8,     // 包含内存使用情况
        IncludeCallStack = 16,      // 包含调用堆栈
        Default = IncludeTimestamp | IncludeLevel  // 默认格式选项
    }

    public class Logger : IDisposable
    {
        private static readonly Logger instance = new Logger();
        public static Logger Instance => instance;

        private readonly string logDirectory;
        private readonly string allLogFilePath; // 保存所有日志的文件路径
        private readonly Dictionary<LogLevel, string> logFilePaths; // 各级别日志文件路径
        private readonly object lockObj = new object();
        private bool enableLogging = true;
        private Dictionary<LogLevel, StringBuilder> logBuffers = new Dictionary<LogLevel, StringBuilder>();
        private Dictionary<LogLevel, bool> enabledLogLevels = new Dictionary<LogLevel, bool>();
        private readonly System.Threading.Timer? flushTimer;
        private LogLevel minimumLogLevel = LogLevel.Info; // 默认记录Info及以上级别的日志
        private LogFormatOptions formatOptions = LogFormatOptions.Default;
        private ConcurrentDictionary<string, Stopwatch> timerDictionary = new ConcurrentDictionary<string, Stopwatch>();
        private bool useSeparateLogFiles = true; // 是否使用分离的日志文件
        private bool keepAllLogFile = true; // 是否保留总日志文件

        public event EventHandler<string>? LogMessage;
        public event EventHandler<LogEventArgs>? LogEntryAdded;

        public bool EnableLogging
        {
            get => enableLogging;
            set => enableLogging = value;
        }

        /// <summary>
        /// 是否使用分离的日志文件（按日志级别分别保存）
        /// </summary>
        public bool UseSeparateLogFiles
        {
            get => useSeparateLogFiles;
            set => useSeparateLogFiles = value;
        }

        /// <summary>
        /// 是否保留总日志文件（含所有启用级别的日志）
        /// </summary>
        public bool KeepAllLogFile
        {
            get => keepAllLogFile;
            set => keepAllLogFile = value;
        }

        /// <summary>
        /// 设置最低日志记录级别
        /// </summary>
        public LogLevel MinimumLogLevel
        {
            get => minimumLogLevel;
            set => minimumLogLevel = value;
        }

        /// <summary>
        /// 日志格式选项
        /// </summary>
        public LogFormatOptions FormatOptions
        {
            get => formatOptions;
            set => formatOptions = value;
        }

        /// <summary>
        /// 启用或禁用特定日志级别
        /// </summary>
        public void SetLogLevelEnabled(LogLevel level, bool enabled)
        {
            enabledLogLevels[level] = enabled;
            // 添加Debug日志记录SetLogLevelEnabled的调用
            if (level == LogLevel.Debug && enabled)
            {
                LogInternal(LogLevel.Debug, $"Debug日志级别已启用");
            }
            else if (level == LogLevel.Debug && !enabled)
            {
                LogInternal(LogLevel.Info, $"Debug日志级别已禁用");
            }

            // 记录更新后的系统日志信息
            LogSystemInfo();
        }

        /// <summary>
        /// 检查指定日志级别是否启用
        /// </summary>
        public bool IsLogLevelEnabled(LogLevel level)
        {
            return enabledLogLevels.TryGetValue(level, out bool enabled) ? enabled : true;
        }

        /// <summary>
        /// 构造函数，初始化日志系统
        /// </summary>
        public Logger()
        {
            try
            {
                logDirectory = Path.Combine(
                    AppDomain.CurrentDomain.BaseDirectory,
                    "logs");

                // 确保日志目录存在
                Directory.CreateDirectory(logDirectory);

                // 使用当前日期作为日志文件名
                string currentDate = DateTime.Now.ToString("yyyy-MM-dd");
                allLogFilePath = Path.Combine(logDirectory, $"log_{currentDate}.txt");

                // 初始化各级别日志文件路径
                logFilePaths = new Dictionary<LogLevel, string>
                {
                    { LogLevel.Debug, Path.Combine(logDirectory, $"debug_{currentDate}.txt") },
                    { LogLevel.Info, Path.Combine(logDirectory, $"info_{currentDate}.txt") },
                    { LogLevel.Warning, Path.Combine(logDirectory, $"warning_{currentDate}.txt") },
                    { LogLevel.Error, Path.Combine(logDirectory, $"error_{currentDate}.txt") },
                    { LogLevel.Fatal, Path.Combine(logDirectory, $"fatal_{currentDate}.txt") }
                };

                // 初始化各级别的日志缓冲区和启用状态
                foreach (LogLevel level in Enum.GetValues(typeof(LogLevel)))
                {
                    logBuffers[level] = new StringBuilder();
                    enabledLogLevels[level] = (int)level >= (int)minimumLogLevel;
                }

                flushTimer = new System.Threading.Timer(FlushLogCallback, null, Timeout.Infinite, Timeout.Infinite);
                StartFlushTimer();

                // 记录系统信息
                LogSystemInfo();
            }
            catch (Exception ex)
            {
                // 创建日志系统失败时的应急处理
                Console.WriteLine($"初始化日志系统失败: {ex.Message}");
                logDirectory = AppDomain.CurrentDomain.BaseDirectory;
                allLogFilePath = Path.Combine(logDirectory, "error_log.txt");
                logFilePaths = new Dictionary<LogLevel, string>();
                foreach (LogLevel level in Enum.GetValues(typeof(LogLevel)))
                {
                    logFilePaths[level] = Path.Combine(logDirectory, $"error_{level.ToString().ToLower()}_log.txt");
                    enabledLogLevels[level] = (int)level >= (int)minimumLogLevel;
                }
            }
        }

        /// <summary>
        /// 记录系统环境信息
        /// </summary>
        private void LogSystemInfo()
        {
            try
            {
                var osVersion = Environment.OSVersion.ToString();
                var clrVersion = Environment.Version.ToString();
                var processorCount = Environment.ProcessorCount;
                var workingSet = Environment.WorkingSet / (1024 * 1024); // MB

                var systemInfo = new StringBuilder();
                systemInfo.AppendLine("=== 系统信息 ===");
                systemInfo.AppendLine($"操作系统: {osVersion}");
                systemInfo.AppendLine($".NET 版本: {clrVersion}");
                systemInfo.AppendLine($"处理器核心数: {processorCount}");
                systemInfo.AppendLine($"当前工作集: {workingSet} MB");
                systemInfo.AppendLine($"程序目录: {AppDomain.CurrentDomain.BaseDirectory}");
                systemInfo.AppendLine($"日志目录: {logDirectory}");
                if (useSeparateLogFiles)
                {
                    systemInfo.AppendLine("日志分级存储已启用，各级别日志文件:");
                    foreach (var kvp in logFilePaths)
                    {
                        // 修复状态显示逻辑
                        bool isEnabled = IsLogLevelEnabled(kvp.Key);
                        string status = isEnabled ? "启用" : "禁用";
                        LogLevel level = kvp.Key;

                        // 对于Debug级别特殊处理一下，确保状态正确
                        if (level == LogLevel.Debug && enabledLogLevels.ContainsKey(level))
                        {
                            status = enabledLogLevels[level] ? "启用" : "禁用";
                        }

                        systemInfo.AppendLine($"  - {kvp.Key}({status}): {Path.GetFileName(kvp.Value)}");
                    }
                }

                if (keepAllLogFile)
                {
                    systemInfo.AppendLine($"总日志文件: {Path.GetFileName(allLogFilePath)} (包含所有启用的日志级别)");
                }
                else
                {
                    systemInfo.AppendLine("总日志文件已禁用，仅使用分级日志文件");
                }

                systemInfo.AppendLine("===============");

                // 使用Info级别记录系统信息，确保总是能看到
                LogInternal(LogLevel.Info, systemInfo.ToString());
            }
            catch (Exception ex)
            {
                LogInternal(LogLevel.Warning, $"记录系统信息时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 记录普通信息（Info级别）
        /// </summary>
        public void Log(string message)
        {
            LogInternal(LogLevel.Info, message);
        }

        /// <summary>
        /// 使用指定日志级别记录信息
        /// </summary>
        public void Log(string message, LogLevel level)
        {
            LogInternal(level, message);
        }

        /// <summary>
        /// 记录调试信息（Debug级别）
        /// </summary>
        public void LogDebug(string message)
        {
            LogInternal(LogLevel.Debug, message);
        }

        /// <summary>
        /// 记录警告信息
        /// </summary>
        public void LogWarning(string message)
        {
            LogInternal(LogLevel.Warning, message);
        }

        /// <summary>
        /// 记录错误信息
        /// </summary>
        public void LogError(string message, Exception ex)
        {
            if (!enableLogging || !IsLogLevelEnabled(LogLevel.Error))
                return;

            var logBuilder = new StringBuilder();

            if ((formatOptions & LogFormatOptions.IncludeTimestamp) == LogFormatOptions.IncludeTimestamp)
                logBuilder.Append($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] ");

            if ((formatOptions & LogFormatOptions.IncludeLevel) == LogFormatOptions.IncludeLevel)
                logBuilder.Append("[错误] ");

            if ((formatOptions & LogFormatOptions.IncludeThreadId) == LogFormatOptions.IncludeThreadId)
                logBuilder.Append($"[线程:{Thread.CurrentThread.ManagedThreadId}] ");

            if ((formatOptions & LogFormatOptions.IncludeMemoryUsage) == LogFormatOptions.IncludeMemoryUsage)
            {
                double memoryMB = Process.GetCurrentProcess().WorkingSet64 / (1024.0 * 1024.0);
                logBuilder.Append($"[内存:{memoryMB:F2}MB] ");
            }

            logBuilder.AppendLine(message);

            // 记录详细的异常信息
            logBuilder.AppendLine($"异常类型: {ex.GetType().FullName}");
            logBuilder.AppendLine($"异常消息: {ex.Message}");

            if ((formatOptions & LogFormatOptions.IncludeCallStack) == LogFormatOptions.IncludeCallStack)
                logBuilder.AppendLine($"堆栈跟踪: {ex.StackTrace}");

            // 记录内部异常信息
            var innerEx = ex.InnerException;
            if (innerEx != null)
            {
                logBuilder.AppendLine("--- 内部异常 ---");
                logBuilder.AppendLine($"类型: {innerEx.GetType().FullName}");
                logBuilder.AppendLine($"消息: {innerEx.Message}");

                if ((formatOptions & LogFormatOptions.IncludeCallStack) == LogFormatOptions.IncludeCallStack)
                    logBuilder.AppendLine($"堆栈: {innerEx.StackTrace}");

                // 递归记录所有内部异常
                while (innerEx.InnerException != null)
                {
                    innerEx = innerEx.InnerException;
                    logBuilder.AppendLine("--- 更深层内部异常 ---");
                    logBuilder.AppendLine($"类型: {innerEx.GetType().FullName}");
                    logBuilder.AppendLine($"消息: {innerEx.Message}");

                    if ((formatOptions & LogFormatOptions.IncludeCallStack) == LogFormatOptions.IncludeCallStack)
                        logBuilder.AppendLine($"堆栈: {innerEx.StackTrace}");
                }
            }

            var logMessage = logBuilder.ToString();
            lock (lockObj)
            {
                // 更新错误日志缓冲区
                logBuffers[LogLevel.Error].AppendLine(logMessage);

                // 如果缓冲区过大，则刷新到文件
                if (logBuffers[LogLevel.Error].Length > 10000)
                {
                    FlushToFile();
                }
            }

            OnLogMessage(logMessage);
            OnLogEntryAdded(new LogEventArgs(LogLevel.Error, message, ex));
        }

        /// <summary>
        /// 记录致命错误信息
        /// </summary>
        public void LogFatal(string message, Exception ex)
        {
            if (!enableLogging || !IsLogLevelEnabled(LogLevel.Fatal))
                return;

            var logBuilder = new StringBuilder();

            if ((formatOptions & LogFormatOptions.IncludeTimestamp) == LogFormatOptions.IncludeTimestamp)
                logBuilder.Append($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] ");

            if ((formatOptions & LogFormatOptions.IncludeLevel) == LogFormatOptions.IncludeLevel)
                logBuilder.Append("[致命错误] ");

            if ((formatOptions & LogFormatOptions.IncludeThreadId) == LogFormatOptions.IncludeThreadId)
                logBuilder.Append($"[线程:{Thread.CurrentThread.ManagedThreadId}] ");

            if ((formatOptions & LogFormatOptions.IncludeMemoryUsage) == LogFormatOptions.IncludeMemoryUsage)
            {
                double memoryMB = Process.GetCurrentProcess().WorkingSet64 / (1024.0 * 1024.0);
                logBuilder.Append($"[内存:{memoryMB:F2}MB] ");
            }

            logBuilder.AppendLine(message);

            // 记录详细的异常信息
            logBuilder.AppendLine($"异常类型: {ex.GetType().FullName}");
            logBuilder.AppendLine($"异常消息: {ex.Message}");

            if ((formatOptions & LogFormatOptions.IncludeCallStack) == LogFormatOptions.IncludeCallStack)
                logBuilder.AppendLine($"堆栈跟踪: {ex.StackTrace}");

            // 记录内部异常信息
            var innerEx = ex.InnerException;
            if (innerEx != null)
            {
                logBuilder.AppendLine("--- 内部异常 ---");
                logBuilder.AppendLine($"类型: {innerEx.GetType().FullName}");
                logBuilder.AppendLine($"消息: {innerEx.Message}");

                if ((formatOptions & LogFormatOptions.IncludeCallStack) == LogFormatOptions.IncludeCallStack)
                    logBuilder.AppendLine($"堆栈: {innerEx.StackTrace}");

                // 递归记录所有内部异常
                while (innerEx.InnerException != null)
                {
                    innerEx = innerEx.InnerException;
                    logBuilder.AppendLine("--- 更深层内部异常 ---");
                    logBuilder.AppendLine($"类型: {innerEx.GetType().FullName}");
                    logBuilder.AppendLine($"消息: {innerEx.Message}");

                    if ((formatOptions & LogFormatOptions.IncludeCallStack) == LogFormatOptions.IncludeCallStack)
                        logBuilder.AppendLine($"堆栈: {innerEx.StackTrace}");
                }
            }

            var logMessage = logBuilder.ToString();
            lock (lockObj)
            {
                // 更新致命错误日志缓冲区
                logBuffers[LogLevel.Fatal].AppendLine(logMessage);

                // 致命错误立即写入文件
                FlushToFile();
            }

            OnLogMessage(logMessage);
            OnLogEntryAdded(new LogEventArgs(LogLevel.Fatal, message, ex));
        }

        /// <summary>
        /// 内部日志记录方法，处理不同级别的日志
        /// </summary>
        private void LogInternal(LogLevel level, string message)
        {
            if (!enableLogging || !IsLogLevelEnabled(level))
                return;

            var levelString = level switch
            {
                LogLevel.Debug => "调试",
                LogLevel.Info => "信息",
                LogLevel.Warning => "警告",
                LogLevel.Error => "错误",
                LogLevel.Fatal => "致命",
                _ => "信息"
            };

            var logBuilder = new StringBuilder();

            if ((formatOptions & LogFormatOptions.IncludeTimestamp) == LogFormatOptions.IncludeTimestamp)
                logBuilder.Append($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] ");

            if ((formatOptions & LogFormatOptions.IncludeLevel) == LogFormatOptions.IncludeLevel)
                logBuilder.Append($"[{levelString}] ");

            if ((formatOptions & LogFormatOptions.IncludeThreadId) == LogFormatOptions.IncludeThreadId)
                logBuilder.Append($"[线程:{Thread.CurrentThread.ManagedThreadId}] ");

            if ((formatOptions & LogFormatOptions.IncludeMemoryUsage) == LogFormatOptions.IncludeMemoryUsage)
            {
                double memoryMB = Process.GetCurrentProcess().WorkingSet64 / (1024.0 * 1024.0);
                logBuilder.Append($"[内存:{memoryMB:F2}MB] ");
            }

            logBuilder.Append(message);
            var logMessage = logBuilder.ToString();

            lock (lockObj)
            {
                // 更新特定级别日志缓冲区
                logBuffers[level].AppendLine(logMessage);

                // 每当缓冲区达到一定大小时写入文件
                if (logBuffers[level].Length > 10000)
                {
                    FlushToFile();
                }
            }

            OnLogMessage(logMessage);
            OnLogEntryAdded(new LogEventArgs(level, message, null));
        }

        /// <summary>
        /// 开始计时器
        /// </summary>
        /// <param name="timerName">计时器名称</param>
        public void StartTimer(string timerName)
        {
            var stopwatch = new Stopwatch();
            stopwatch.Start();
            timerDictionary[timerName] = stopwatch;
        }

        /// <summary>
        /// 停止计时器并记录耗时
        /// </summary>
        /// <param name="timerName">计时器名称</param>
        /// <param name="message">附加消息</param>
        public void StopTimer(string timerName, string message = "")
        {
            if (timerDictionary.TryRemove(timerName, out Stopwatch? stopwatch))
            {
                stopwatch.Stop();
                var elapsed = stopwatch.Elapsed;

                string formattedTime;
                if (elapsed.TotalSeconds < 1)
                    formattedTime = $"{elapsed.TotalMilliseconds:F2}毫秒";
                else if (elapsed.TotalMinutes < 1)
                    formattedTime = $"{elapsed.TotalSeconds:F2}秒";
                else
                    formattedTime = $"{elapsed.TotalMinutes:F2}分钟";

                string logText = string.IsNullOrEmpty(message) ?
                    $"[计时] {timerName}: {formattedTime}" :
                    $"[计时] {timerName}: {formattedTime} - {message}";

                Log(logText);
            }
        }

        /// <summary>
        /// 获取当前内存使用情况并记录日志
        /// </summary>
        public void LogMemoryUsage(string context = "")
        {
            try
            {
                using (var process = Process.GetCurrentProcess())
                {
                    double workingSetMB = process.WorkingSet64 / (1024.0 * 1024.0);
                    double privateMemoryMB = process.PrivateMemorySize64 / (1024.0 * 1024.0);
                    double peakWorkingSetMB = process.PeakWorkingSet64 / (1024.0 * 1024.0);

                    string message = string.IsNullOrEmpty(context) ?
                        $"内存使用情况 - 工作集: {workingSetMB:F2}MB, 私有内存: {privateMemoryMB:F2}MB, 峰值: {peakWorkingSetMB:F2}MB" :
                        $"内存使用情况({context}) - 工作集: {workingSetMB:F2}MB, 私有内存: {privateMemoryMB:F2}MB, 峰值: {peakWorkingSetMB:F2}MB";

                    Log(message);
                }
            }
            catch (Exception ex)
            {
                LogWarning($"获取内存使用情况失败: {ex.Message}");
            }
        }

        public void ClearLog()
        {
            lock (lockObj)
            {
                // 清空内存缓冲区
                foreach (var level in logBuffers.Values)
                {
                    level.Clear();
                }

                try
                {
                    // 清空主日志文件
                    if (File.Exists(allLogFilePath))
                    {
                        File.WriteAllText(allLogFilePath, string.Empty);
                        Console.WriteLine($"已清空主日志文件: {Path.GetFileName(allLogFilePath)}");
                    }

                    // 清空所有分级日志文件
                    if (useSeparateLogFiles && logFilePaths != null)
                    {
                        foreach (var kvp in logFilePaths)
                        {
                            string levelFilePath = kvp.Value;
                            if (File.Exists(levelFilePath))
                            {
                                File.WriteAllText(levelFilePath, string.Empty);
                                Console.WriteLine($"已清空{kvp.Key}级别日志文件: {Path.GetFileName(levelFilePath)}");
                            }
                        }
                    }

                    // 清空logs目录下的所有其他日志文件（包括历史日志文件）
                    if (Directory.Exists(logDirectory))
                    {
                        var logFiles = Directory.GetFiles(logDirectory, "*.txt");
                        foreach (string logFile in logFiles)
                        {
                            try
                            {
                                // 检查是否是当前正在使用的日志文件，如果是则清空内容，否则删除
                                string fileName = Path.GetFileName(logFile);
                                bool isCurrentLogFile = fileName.Equals(Path.GetFileName(allLogFilePath), StringComparison.OrdinalIgnoreCase) ||
                                                      logFilePaths.Values.Any(path => fileName.Equals(Path.GetFileName(path), StringComparison.OrdinalIgnoreCase));

                                if (isCurrentLogFile)
                                {
                                    // 当前使用的日志文件已经在上面处理过了，跳过
                                    continue;
                                }
                                else
                                {
                                    // 删除历史日志文件
                                    File.Delete(logFile);
                                    Console.WriteLine($"已删除历史日志文件: {fileName}");
                                }
                            }
                            catch (Exception fileEx)
                            {
                                Console.WriteLine($"处理日志文件 {Path.GetFileName(logFile)} 时出错: {fileEx.Message}");
                            }
                        }
                    }

                    Console.WriteLine("日志清空操作完成");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"清除日志文件失败: {ex.Message}");
                    // 记录到事件日志或其他地方，因为日志系统本身可能有问题
                    try
                    {
                        // 尝试写入Windows事件日志
                        System.Diagnostics.EventLog.WriteEntry("AsposeWordFormatter",
                            $"清除日志文件时发生错误: {ex.Message}",
                            System.Diagnostics.EventLogEntryType.Warning);
                    }
                    catch
                    {
                        // 如果连事件日志都写不了，就只能输出到控制台了
                    }
                }
            }
        }

        private void StartFlushTimer()
        {
            flushTimer?.Change(TimeSpan.FromSeconds(10), TimeSpan.FromSeconds(10));
        }

        private void StopFlushTimer()
        {
            flushTimer?.Change(Timeout.Infinite, Timeout.Infinite);
        }

        private void FlushLogCallback(object? state)
        {
            FlushToFile();
        }

        public async Task FlushLogAsync()
        {
            if (!logBuffers.Values.Any(buffer => buffer.Length > 0))
                return;

            try
            {
                // 确保日志目录存在
                if (!Directory.Exists(logDirectory))
                {
                    Directory.CreateDirectory(logDirectory);
                }

                // 更新所有日志文件的日期
                string currentDate = DateTime.Now.ToString("yyyy-MM-dd");
                UpdateLogFilePaths(currentDate);

                // 处理各级别日志
                foreach (var level in logBuffers.Keys.ToList())
                {
                    if (!IsLogLevelEnabled(level))
                        continue;

                    string levelContent;
                    lock (lockObj)
                    {
                        levelContent = logBuffers[level].ToString();
                        logBuffers[level].Clear();
                    }

                    if (!string.IsNullOrEmpty(levelContent))
                    {
                        // 写入级别日志文件
                        if (useSeparateLogFiles && logFilePaths.ContainsKey(level))
                        {
                            string levelPath = logFilePaths[level];
                            using (var writer = new StreamWriter(levelPath, true, Encoding.UTF8))
                            {
                                await writer.WriteAsync(levelContent);
                            }
                        }

                        // 写入总日志文件
                        if (keepAllLogFile)
                        {
                            using (var writer = new StreamWriter(allLogFilePath, true, Encoding.UTF8))
                            {
                                await writer.WriteAsync(levelContent);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"异步写入日志失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 将日志从缓冲区写入文件
        /// </summary>
        private void FlushToFile()
        {
            if (!logBuffers.Values.Any(buffer => buffer.Length > 0))
                return;

            try
            {
                // 确保日志目录存在
                if (!Directory.Exists(logDirectory))
                {
                    Directory.CreateDirectory(logDirectory);
                }

                // 更新所有日志文件的日期
                string currentDate = DateTime.Now.ToString("yyyy-MM-dd");
                UpdateLogFilePaths(currentDate);

                // 如果启用分级日志存储，则分别写入各级别日志文件
                if (useSeparateLogFiles)
                {
                    foreach (var level in logBuffers.Keys.ToList())
                    {
                        if (!IsLogLevelEnabled(level))
                            continue;

                        string levelLogContent;
                        lock (lockObj)
                        {
                            levelLogContent = logBuffers[level].ToString();
                            logBuffers[level].Clear();
                        }

                        if (!string.IsNullOrEmpty(levelLogContent) && logFilePaths.ContainsKey(level))
                        {
                            WriteToLogFile(logFilePaths[level], levelLogContent);
                        }
                    }
                }

                // 如果启用总日志文件，则将所有级别的日志汇总写入
                if (keepAllLogFile)
                {
                    StringBuilder allLogsContent = new StringBuilder();

                    // 将所有日志级别的内容合并
                    foreach (var level in logBuffers.Keys.ToList())
                    {
                        if (!IsLogLevelEnabled(level))
                            continue;

                        string levelLogContent;
                        lock (lockObj)
                        {
                            levelLogContent = logBuffers[level].ToString();
                            // 不清除缓冲区，因为已经在上面的分级存储中清除了
                        }

                        if (!string.IsNullOrEmpty(levelLogContent))
                        {
                            allLogsContent.Append(levelLogContent);
                        }
                    }

                    if (allLogsContent.Length > 0)
                    {
                        WriteToLogFile(allLogFilePath, allLogsContent.ToString());
                    }
                }
                else
                {
                    // 如果未启用总日志文件，直接清除所有缓冲区
                    foreach (var level in logBuffers.Keys.ToList())
                    {
                        lock (lockObj)
                        {
                            if (logBuffers[level].Length > 0)
                            {
                                logBuffers[level].Clear();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"写入日志文件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 写入日志内容到指定文件
        /// </summary>
        private void WriteToLogFile(string filePath, string content)
        {
            try
            {
                // 如果日志文件大于10MB，创建新的日志文件
                if (File.Exists(filePath) && new FileInfo(filePath).Length > 10 * 1024 * 1024)
                {
                    string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    string directory = Path.GetDirectoryName(filePath) ?? "";
                    string fileName = Path.GetFileNameWithoutExtension(filePath);
                    string extension = Path.GetExtension(filePath);
                    string newFilePath = Path.Combine(directory, $"{fileName}_{timestamp}{extension}");

                    File.Move(filePath, newFilePath);
                }

                // 追加写入日志文件
                File.AppendAllText(filePath, content);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"写入日志文件 {filePath} 失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新日志文件路径，确保使用当前日期
        /// </summary>
        private void UpdateLogFilePaths(string currentDate)
        {
            // 检查当前日志文件名中的日期是否是今天的日期
            string fileDate = Path.GetFileNameWithoutExtension(allLogFilePath).Split('_').LastOrDefault() ?? "";

            if (fileDate != currentDate)
            {
                string newAllLogFilePath = Path.Combine(logDirectory, $"log_{currentDate}.txt");
                Dictionary<LogLevel, string> newLogFilePaths = new Dictionary<LogLevel, string>();

                foreach (LogLevel level in Enum.GetValues(typeof(LogLevel)))
                {
                    string levelName = level.ToString().ToLower();
                    newLogFilePaths[level] = Path.Combine(logDirectory, $"{levelName}_{currentDate}.txt");
                }

                // 使用反射更新readonly字段
                typeof(Logger).GetField("allLogFilePath", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic)
                    ?.SetValue(this, newAllLogFilePath);

                typeof(Logger).GetField("logFilePaths", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic)
                    ?.SetValue(this, newLogFilePaths);

                // 记录日志文件路径更新信息
                string message = $"日志文件日期已更新为: {currentDate}";
                LogInternal(LogLevel.Info, message);
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                StopFlushTimer();
                flushTimer?.Dispose();
                FlushToFile();
            }
        }

        protected virtual void OnLogMessage(string message)
        {
            LogMessage?.Invoke(this, message);
        }

        protected virtual void OnLogEntryAdded(LogEventArgs args)
        {
            LogEntryAdded?.Invoke(this, args);
        }
    }

    /// <summary>
    /// 日志事件参数类，提供结构化日志信息
    /// </summary>
    public class LogEventArgs : EventArgs
    {
        public LogLevel Level { get; }
        public string Message { get; }
        public Exception? Exception { get; }
        public DateTime Timestamp { get; }

        public LogEventArgs(LogLevel level, string message, Exception? exception)
        {
            Level = level;
            Message = message;
            Exception = exception;
            Timestamp = DateTime.Now;
        }
    }
}